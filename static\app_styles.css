/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

/* Base styles with custom font */
body {
    background-color: #f0f2f6;
    font-family: 'Poppins', sans-serif;
    color: #333;
}

/* Navbar styling */
.navbar {
    background-color: #6c5ce7;
    padding: 15px;
    text-align: center;
    color: white;
    font-size: 24px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    border-radius: 10px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    letter-spacing: 1px;
}

/* Content container */
.content-container {
    text-align: center;
    max-width: 1200px;
    margin: auto;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
}

/* Button styling */
.stButton > button {
    background-color: #6c5ce7;
    color: white;
    font-size: 16px;
    padding: 12px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    width: 100%;
    margin-top: 10px;
    transition: all 0.2s ease;
}

.stButton > button:hover {
    background-color: #5849c4;
    transform: translateY(-2px);
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}

/* Image container and dashboard styles */
.image-container {
    width: 100%;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 15px;
}

.dashboard-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.dashboard-column {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    margin: 10px;
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e1e1;
}

.dashboard-column:hover {
    transform: translateY(-5px);
    box-shadow: 0px 6px 15px rgba(0, 0, 0, 0.15);
}

.dashboard-column:hover .dashboard-image {
    transform: scale(1.05);
}

/* Dashboard column headings */
.dashboard-column h3 {
    color: #333;
    margin: 15px 0;
    font-size: 22px;
    font-family: 'Roboto Slab', serif;
    font-weight: 700;
    letter-spacing: 0.5px;
}

/* Dashboard column paragraphs */
.dashboard-column p {
    color: #666;
    font-size: 16px;
    line-height: 1.5;
    font-family: 'Poppins', sans-serif;
    font-weight: 300;
}

/* Auth container styles with custom fonts */
.auth-container {
    background-color: white;
    border-radius: 15px;
    padding: 30px;
    margin: 20px auto;
    max-width: 500px;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e1e1;
}

.auth-title {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
    font-size: 28px;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    letter-spacing: 1px;
}

/* Header login button container */
.header-login-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 100%;
    padding-top: 10px;
}

/* Special styling for header login button */
div[data-testid="column"]:nth-child(3) .stButton > button {
    float: right !important;
    margin-top: 5px !important;
    font-size: 0.9em !important;
    padding: 8px 15px !important;
    background-color: #6c5ce7 !important;
    color: white !important;
    border: none !important;
    border-radius: 5px !important;
    transition: all 0.3s ease !important;
}

div[data-testid="column"]:nth-child(3) .stButton > button:hover {
    background-color: #5849c4 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

/* Form styling for auth pages */
form[data-testid="stForm"] {
    background-color: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

/* Back button styling */
button[kind="secondary"] {
    background-color: #f0f0f0 !important;
    color: #333 !important;
    border: 1px solid #ddd !important;
}

button[kind="secondary"]:hover {
    background-color: #e0e0e0 !important;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 2s ease-in-out;
}

/* Form inputs */
.stTextInput > div > div > input {
    border-radius: 8px;
    border: 1px solid #6c5ce7;
    padding: 10px;
    transition: all 0.3s ease;
}

.stTextInput > div > div > input:focus {
    border-color: #5849c4;
    box-shadow: 0 0 5px rgba(108, 92, 231, 0.3);
}

/* Custom styles for Streamlit elements */
.stTextInput > div > div > input {
    font-family: 'Poppins', sans-serif !important;
    font-size: 16px !important;
}

.stButton > button {
    font-family: 'Montserrat', sans-serif !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
}

/* Custom heading styles */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Custom paragraph styles */
p {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    line-height: 1.6;
}

/* Custom styles for success/error messages */
.stSuccess, .stError {
    font-family: 'Poppins', sans-serif !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
}

/* Profile Picture Styles for Sidebar */
.profile-container {
    display: flex;
    align-items: center;
    padding: 15px 10px;
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1), rgba(224, 176, 255, 0.1));
    border-radius: 12px;
    margin: 10px 0;
    border: 1px solid rgba(108, 92, 231, 0.2);
    transition: all 0.3s ease;
}

.profile-container:hover {
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.15), rgba(224, 176, 255, 0.15));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 92, 231, 0.2);
}

.profile-picture-container {
    margin-right: 12px;
    flex-shrink: 0;
}

.profile-picture-container svg {
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.profile-picture-container:hover svg {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.profile-info {
    flex: 1;
    min-width: 0;
}

.profile-name {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 16px;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.profile-email {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Tabs styling with custom font */
.stTabs [data-baseweb="tab-list"] {
    gap: 10px;
    background-color: #f5f6fa;
    border-radius: 10px;
    padding: 5px;
}

.stTabs [data-baseweb="tab"] {
    background-color: #6c5ce7;
    border-radius: 8px;
    color: white;
    transition: all 0.3s ease;
}

.stTabs [data-baseweb="tab"]:hover {
    background-color: #5849c4;
}

/* Custom animation for text */
.animated-text {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Text Animation Collection */
.text-focus-in {
    animation: text-focus-in 1s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
}

@keyframes text-focus-in {
    0% {
        filter: blur(12px);
        opacity: 0;
    }
    100% {
        filter: blur(0px);
        opacity: 1;
    }
}

.tracking-in-expand {
    animation: tracking-in-expand 0.7s cubic-bezier(0.215, 0.610, 0.355, 1.000) both;
}

@keyframes tracking-in-expand {
    0% {
        letter-spacing: -0.5em;
        opacity: 0;
    }
    40% {
        opacity: 0.6;
    }
    100% {
        opacity: 1;
    }
}

.bounce-in-top {
    animation: bounce-in-top 1.1s both;
}

@keyframes bounce-in-top {
    0% {
        transform: translateY(-500px);
        animation-timing-function: ease-in;
        opacity: 0;
    }
    38% {
        transform: translateY(0);
        animation-timing-function: ease-out;
        opacity: 1;
    }
    55% {
        transform: translateY(-65px);
        animation-timing-function: ease-in;
    }
    72% {
        transform: translateY(0);
        animation-timing-function: ease-out;
    }
    81% {
        transform: translateY(-28px);
        animation-timing-function: ease-in;
    }
    90% {
        transform: translateY(0);
        animation-timing-function: ease-out;
    }
    95% {
        transform: translateY(-8px);
        animation-timing-function: ease-in;
    }
    100% {
        transform: translateY(0);
        animation-timing-function: ease-out;
    }
}

/* Enhanced Chatbot Interface Styling */
.chat-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin: 20px auto;
    max-width: 800px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e1e1e1;
}

.chat-message {
    display: flex;
    align-items: flex-start;
    margin: 20px 0;
    animation: slide-in 0.3s ease-out;
}

.chat-message.user {
    flex-direction: row-reverse;
}

.message-content {
    max-width: 80%;
    padding: 15px;
    border-radius: 15px;
    margin: 0 10px;
    font-family: 'Poppins', sans-serif;
    position: relative;
}

.user .message-content {
    background: #6c5ce7;
    color: white;
    border-top-right-radius: 0;
}

.assistant .message-content {
    background: #f5f6fa;
    color: #333;
    border-top-left-radius: 0;
    border: 1px solid #e1e1e1;
}

.chat-input-container {
    background: white;
    border-radius: 15px;
    padding: 15px;
    margin-top: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e1e1;
}

.chat-input {
    width: 100%;
    padding: 12px;
    border: 2px solid #6c5ce7;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    transition: all 0.3s ease;
}

.chat-input:focus {
    border-color: #5849c4;
    box-shadow: 0 0 10px rgba(108, 92, 231, 0.2);
    outline: none;
}

/* Typing indicator animation */
.typing-indicator {
    display: flex;
    padding: 10px;
    gap: 5px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: #6c5ce7;
    border-radius: 50%;
    animation: typing-bounce 0.8s infinite;
}

.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing-bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes slide-in {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pop-in {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Chat Loading Animation Styles */
.chat-loading {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 15px;
    margin: 10px 0;
    background: rgba(108, 92, 231, 0.1);
    border-radius: 10px;
    max-width: 200px;
}

.loading-dots {
    display: flex;
    align-items: center;
    gap: 5px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    background: #6c5ce7;
    border-radius: 50%;
    opacity: 0;
    animation: loadingDot 1.5s infinite;
}

.loading-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes loadingDot {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0);
        opacity: 0;
    }
}

/* Thinking animation */
.thinking-animation {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(108, 92, 231, 0.1);
    border-radius: 10px;
    margin: 10px 0;
}

.thinking-icon {
    font-size: 20px;
    animation: thinking 1s infinite;
}

.thinking-text {
    color: #6c5ce7;
    font-weight: 500;
    animation: fadeInOut 2s infinite;
}

@keyframes thinking {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Stream effect for text */
.stream-effect {
    border-right: 2px solid #6c5ce7;
    animation: blink 0.75s step-end infinite;
}

@keyframes blink {
    from, to { border-color: transparent; }
    50% { border-color: #6c5ce7; }
}

/* Message appear animation */
.message-appear {
    animation: messageSlide 0.3s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
}

@keyframes messageSlide {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Glass morphism effects */
.glass-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Page transitions */
.page-transition {
    animation: page-fade-in 0.5s ease-out;
}

@keyframes page-fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Typing animation */
.typing-animation {
    display: inline-flex;
    gap: 4px;
    padding: 12px 16px;
    background: rgba(108, 92, 231, 0.1);
    border-radius: 20px;
    margin: 8px 0;
}

.typing-animation span {
    width: 8px;
    height: 8px;
    background: #6c5ce7;
    border-radius: 50%;
    animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-animation span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typing-bounce {
    0%, 80%, 100% { 
        transform: scale(0);
        opacity: 0.3;
    }
    40% { 
        transform: scale(1);
        opacity: 1;
    }
}

/* Chat message animations */
.stChatMessage {
    animation: message-fade 0.3s ease forwards;
}

@keyframes message-fade {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Chat container styles */
.chat-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Message styles */
.stChatMessage [data-testid="stMarkdownContainer"] {
    padding: 12px 16px;
    border-radius: 12px;
    background: rgba(108, 92, 231, 0.05);
    transition: all 0.3s ease;
}

.stChatMessage [data-testid="stMarkdownContainer"]:hover {
    background: rgba(108, 92, 231, 0.1);
}

/* Input field styles */
.stChatInputContainer {
    border-top: 1px solid rgba(108, 92, 231, 0.1);
    padding-top: 20px;
    margin-top: 20px;
}

.stChatInputContainer textarea {
    border-radius: 20px;
    border: 2px solid rgba(108, 92, 231, 0.2);
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.stChatInputContainer textarea:focus {
    border-color: #6c5ce7;
    box-shadow: 0 0 0 2px rgba(108, 92, 231, 0.1);
}

/* Footer styles */
.footer {
    background: rgba(108, 92, 231, 0.1);
    padding: 30px 20px;
    margin-top: 50px;
    border-top: 1px solid rgba(108, 92, 231, 0.2);
    width: 100%;
    bottom: 0;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 30px;
}

.footer-section {
    flex: 1;
    min-width: 250px;
}

.footer-logo {
    font-family: 'Montserrat', sans-serif;
    font-size: 24px;
    font-weight: 600;
    color: #6c5ce7;
    margin-bottom: 10px;
}

.footer-text {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.footer-links {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin: 15px 0;
}

.footer-link {
    color: #6c5ce7;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #5046c0;
    text-decoration: underline;
}

.footer-social {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-icon {
    font-size: 24px;
    color: #6c5ce7;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-3px);
}

.footer-credits {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(108, 92, 231, 0.2);
}

.footer-credits p {
    color: #888;
    font-size: 12px;
    margin: 5px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }

    .footer-social {
        justify-content: center;
    }
}

/* Add these styles to your existing CSS */
.welcome-container {
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1), rgba(224, 176, 255, 0.1));
    border-radius: 15px;
    padding: 30px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(224, 176, 255, 0.2);
}

.welcome-container h1 {
    color: #6c5ce7;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 2.5em;
}

.welcome-container p {
    color: #666;
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    margin: 10px 0;
}

.gradient-text {
    background: linear-gradient(135deg, #6c5ce7, #E0B0FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Quiz page specific styles */
.quiz-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.quiz-header {
    text-align: center;
    margin-bottom: 30px;
}

.quiz-section {
    background: rgba(108, 92, 231, 0.05);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.quiz-question {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* Style for radio buttons in quiz */
.stRadio > label {
    padding: 10px !important;
    background: rgba(224, 176, 255, 0.05);
    border-radius: 5px;
    margin: 5px 0;
    transition: all 0.3s ease;
}

.stRadio > label:hover {
    background: rgba(224, 176, 255, 0.1);
    transform: translateX(5px);
}

/* Quiz submit button */
.quiz-submit {
    background: linear-gradient(135deg, #6c5ce7, #E0B0FF) !important;
    color: white !important;
    padding: 12px 24px !important;
    border-radius: 8px !important;
    border: none !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.quiz-submit:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(108, 92, 231, 0.2) !important;
}

/* Landing Page Styles */
.login-button {
    background-color: #6c5ce7;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.login-button:hover {
    background-color: #5849c4;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.site-title {
    text-align: center;
    color: #6c5ce7;
    font-family: 'Montserrat', sans-serif;
    margin-top: 10px;
}

.section-title {
    color: #6c5ce7;
    font-family: 'Montserrat', sans-serif;
    font-size: 2em;
    margin-top: 40px;
    margin-bottom: 20px;
    text-align: center;
}

.feature-card {
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1), rgba(224, 176, 255, 0.1));
    border-radius: 10px;
    padding: 20px;
    margin: 10px 0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 2.5em;
    color: #6c5ce7;
    margin-bottom: 15px;
    text-align: center;
}

.feature-title {
    font-weight: 600;
    color: #2D3748;
    margin-bottom: 10px;
    text-align: center;
}

.feature-text {
    color: #4A5568;
    text-align: center;
}

.hero-section {
    text-align: center;
    margin: 50px 0;
    padding: 40px;
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1), rgba(224, 176, 255, 0.1));
    border-radius: 15px;
}

.hero-title {
    font-family: Montserrat, sans-serif;
    color: #6c5ce7;
    font-size: 3em;
    margin-bottom: 20px;
}

.hero-text {
    font-family: Poppins, sans-serif;
    color: #4A5568;
    font-size: 1.2em;
    max-width: 800px;
    margin: 0 auto 30px auto;
}

.hero-button {
    background-color: #6c5ce7;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 5px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.hero-button:hover {
    background-color: #5849c4;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.about-section {
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1), rgba(224, 176, 255, 0.1));
    border-radius: 15px;
    padding: 30px;
    margin: 20px 0;
}

.about-text {
    font-family: Poppins, sans-serif;
    color: #4A5568;
    font-size: 1.1em;
    line-height: 1.6;
}

.cta-section {
    text-align: center;
    margin: 50px 0;
    padding: 40px;
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.2), rgba(224, 176, 255, 0.2));
    border-radius: 15px;
}

.cta-title {
    font-family: Montserrat, sans-serif;
    color: #6c5ce7;
    margin-bottom: 20px;
}

.cta-text {
    font-family: Poppins, sans-serif;
    color: #4A5568;
    font-size: 1.1em;
    max-width: 700px;
    margin: 0 auto 30px auto;
}

/* Streamlit button styling for landing page */
.stButton > button[data-testid="stButton"] {
    background-color: #6c5ce7 !important;
    color: white !important;
    font-family: 'Montserrat', sans-serif !important;
    font-weight: 600 !important;
    padding: 10px 25px !important;
    border-radius: 5px !important;
    border: none !important;
    transition: all 0.3s ease !important;
    margin: 10px auto 30px auto !important;
    display: block !important;
}

.stButton > button[data-testid="stButton"]:hover {
    background-color: #5849c4 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2) !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Special styling for header login button */
div[data-testid="column"]:nth-child(3) .stButton > button {
    float: right !important;
    margin-top: 15px !important;
    font-size: 0.9em !important;
    padding: 8px 15px !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}

/* Center the hero and CTA buttons */
div:has(> button[key="start_journey_button"]),
div:has(> button[key="get_started_button"]) {
    display: flex !important;
    justify-content: center !important;
    margin: 0 auto 30px auto !important;
}


