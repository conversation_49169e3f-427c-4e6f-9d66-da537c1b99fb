{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "PpQVyND7nTWZ", "outputId": "40cad58e-dab5-464b-ace5-372565f39f89"}, "outputs": [], "source": ["!pip install --upgrade pandas==2.0.3"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 200}, "id": "oiSumuPNni_x", "outputId": "260f5652-c157-4664-9ff1-5f408d787721"}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 512}, "id": "Mzs_Hl7cmcgb", "outputId": "3242baa0-2a3a-4080-f9ea-8a03d6cbd502"}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "df = pd.read_csv('cleaned_dataset.csv')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 141}, "id": "38McdW_IoinH", "outputId": "f7c907fe-d639-49ba-8e0a-aa28216fdaf8"}, "outputs": [{"data": {"text/plain": ["(6482, 16)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.shape"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 264}, "id": "Zk7N8C17mab6", "outputId": "6daf1fd3-2467-420c-9f54-45b4ac2c056b"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Database Fundamentals</th>\n", "      <th>Computer Architecture</th>\n", "      <th>Distributed Computing Systems</th>\n", "      <th>Cyber Security</th>\n", "      <th>Networking</th>\n", "      <th>Software Development</th>\n", "      <th>Programming Skills</th>\n", "      <th>Project Management</th>\n", "      <th>Computer Forensics Fundamentals</th>\n", "      <th>Technical Communication</th>\n", "      <th>AI ML</th>\n", "      <th>Software Engineering</th>\n", "      <th>Business Analysis</th>\n", "      <th>Data Science</th>\n", "      <th>Troubleshooting skills</th>\n", "      <th>Role</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>6143</td>\n", "      <td>6079</td>\n", "      <td>6100</td>\n", "      <td>6115</td>\n", "      <td>6096</td>\n", "      <td>6108</td>\n", "      <td>6090</td>\n", "      <td>6087</td>\n", "      <td>6073</td>\n", "      <td>6098</td>\n", "      <td>6109</td>\n", "      <td>6090</td>\n", "      <td>6101</td>\n", "      <td>6105</td>\n", "      <td>6086</td>\n", "      <td>6482</td>\n", "    </tr>\n", "    <tr>\n", "      <th>unique</th>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>12</td>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>11</td>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>13</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>top</th>\n", "      <td>Excellent</td>\n", "      <td>Excellent</td>\n", "      <td>Intermediate</td>\n", "      <td>Intermediate</td>\n", "      <td>Intermediate</td>\n", "      <td>Excellent</td>\n", "      <td>Intermediate</td>\n", "      <td>Intermediate</td>\n", "      <td>Intermediate</td>\n", "      <td>Excellent</td>\n", "      <td>Excellent</td>\n", "      <td>Excellent</td>\n", "      <td>Excellent</td>\n", "      <td>Intermediate</td>\n", "      <td>Excellent</td>\n", "      <td>Cyber Security Specialist</td>\n", "    </tr>\n", "    <tr>\n", "      <th>freq</th>\n", "      <td>964</td>\n", "      <td>944</td>\n", "      <td>1044</td>\n", "      <td>966</td>\n", "      <td>956</td>\n", "      <td>952</td>\n", "      <td>962</td>\n", "      <td>1028</td>\n", "      <td>953</td>\n", "      <td>950</td>\n", "      <td>948</td>\n", "      <td>962</td>\n", "      <td>956</td>\n", "      <td>960</td>\n", "      <td>959</td>\n", "      <td>450</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Database Fundamentals Computer Architecture  \\\n", "count                   6143                  6079   \n", "unique                    13                    13   \n", "top                Excellent             Excellent   \n", "freq                     964                   944   \n", "\n", "       Distributed Computing Systems Cyber Security    Networking  \\\n", "count                           6100           6115          6096   \n", "unique                            12             13            13   \n", "top                     Intermediate   Intermediate  Intermediate   \n", "freq                            1044            966           956   \n", "\n", "       Software Development Programming Skills Project Management  \\\n", "count                  6108               6090               6087   \n", "unique                   13                 13                 11   \n", "top               Excellent       Intermediate       Intermediate   \n", "freq                    952                962               1028   \n", "\n", "       Computer Forensics Fundamentals Technical Communication      AI ML  \\\n", "count                             6073                    6098       6109   \n", "unique                              13                      13         13   \n", "top                       Intermediate               Excellent  Excellent   \n", "freq                               953                     950        948   \n", "\n", "       Software Engineering Business Analysis  Data Science  \\\n", "count                  6090              6101          6105   \n", "unique                   13                13            13   \n", "top               Excellent         Excellent  Intermediate   \n", "freq                    962               956           960   \n", "\n", "       Troubleshooting skills                       Role  \n", "count                    6086                       6482  \n", "unique                     13                         15  \n", "top                 Excellent  Cyber Security Specialist  \n", "freq                      959                        450  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5mHGRZwUmaZ8", "outputId": "bf980dca-ca91-45bd-c205-9063503d13c0"}, "outputs": [{"data": {"text/plain": ["Index(['Database Fundamentals', 'Computer Architecture',\n", "       'Distributed Computing Systems', 'Cyber Security', 'Networking',\n", "       'Software Development', 'Programming Skills', 'Project Management',\n", "       'Computer Forensics Fundamentals', 'Technical Communication', 'AI ML',\n", "       'Software Engineering', 'Business Analysis', 'Data Science',\n", "       'Troubleshooting skills', 'Role'],\n", "      dtype='object')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Print the names of all columns in the DataFrame\n", "df.columns\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 586}, "id": "Cpn8HRttmaXr", "outputId": "2c988a16-abf8-4a62-a564-d7c29857d811"}, "outputs": [{"data": {"text/plain": ["Database Fundamentals              339\n", "Computer Architecture              403\n", "Distributed Computing Systems      382\n", "Cyber Security                     367\n", "Networking                         386\n", "Software Development               374\n", "Programming Skills                 392\n", "Project Management                 395\n", "Computer Forensics Fundamentals    409\n", "Technical Communication            384\n", "AI ML                              373\n", "Software Engineering               392\n", "Business Analysis                  381\n", "Data Science                       377\n", "Troubleshooting skills             396\n", "Role                                 0\n", "dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.isna().sum()"]}, {"cell_type": "markdown", "metadata": {"id": "UqQfh7I8wBtj"}, "source": ["## try to drop null vlues\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "LeqviBlUmaQu"}, "outputs": [], "source": ["droped = df.dropna()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 586}, "id": "sBTFgF8uor_z", "outputId": "59c67f28-9a01-4e2c-fa90-3de9a3ad1fa3"}, "outputs": [{"data": {"text/plain": ["Database Fundamentals              0\n", "Computer Architecture              0\n", "Distributed Computing Systems      0\n", "Cyber Security                     0\n", "Networking                         0\n", "Software Development               0\n", "Programming Skills                 0\n", "Project Management                 0\n", "Computer Forensics Fundamentals    0\n", "Technical Communication            0\n", "AI ML                              0\n", "Software Engineering               0\n", "Business Analysis                  0\n", "Data Science                       0\n", "Troubleshooting skills             0\n", "Role                               0\n", "dtype: int64"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["droped.isna().sum()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "a9xNHwuBowHk", "outputId": "66ca692a-dd7e-41fd-a95a-e9bbbdfe610d"}, "outputs": [{"data": {"text/plain": ["(2694, 16)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# there are too much null values so\n", "droped.shape"]}, {"cell_type": "markdown", "metadata": {"id": "qPbcT7fXwMOS"}, "source": ["## droping non it jobroles"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_7lhJEr8maN8", "outputId": "de3808f7-b329-4bba-9e56-c1791b3a0337"}, "outputs": [{"data": {"text/plain": ["array(['API Specialist', 'Graphics Designer', 'Technical Writer',\n", "       'AI ML Specialist', 'Helpdesk Engineer', 'Hardware Engineer',\n", "       'Software Developer', 'Customer Service Executive',\n", "       'Business Analyst', 'Networking Engineer', 'Software tester',\n", "       'Cyber Security Specialist', 'Data Scientist',\n", "       'Information Security Specialist', 'Database Administrator'],\n", "      dtype=object)"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Role'].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QoeZeoT5t51y", "outputId": "ded417d7-b21b-49c3-d0f8-44f55ccc9aa4"}, "outputs": [{"data": {"text/plain": ["(4349, 16)"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["roles_drop =['API Specialist', 'Graphics Designer', 'Technical Writer',\n", "        'Helpdesk Engineer', 'Customer Service Executive']\n", "\n", "# Use boolean indexing to select rows where 'Role' is NOT in roles_drop\n", "df = df[~df['Role'].isin(roles_drop)]\n", "\n", "# Display the updated DataFrame shape\n", "df.shape"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vA4jOgOPu07O", "outputId": "30fab126-56b1-4909-97e5-9ba49b446d8e"}, "outputs": [{"data": {"text/plain": ["array(['AI ML Specialist', 'Hardware Engineer', 'Software Developer',\n", "       'Business Analyst', 'Networking Engineer', 'Software tester',\n", "       'Cyber Security Specialist', 'Data Scientist',\n", "       'Information Security Specialist', 'Database Administrator'],\n", "      dtype=object)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Role'].unique()"]}, {"cell_type": "markdown", "metadata": {"id": "lqksh9rjwl6-"}, "source": ["## dropping unnecesrry columns\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "F-SEVmc5w0OQ", "outputId": "66afbd51-38ac-4a17-9fdc-4d20e021d94e"}, "outputs": [{"data": {"text/plain": ["Index(['Database Fundamentals', 'Computer Architecture',\n", "       'Distributed Computing Systems', 'Cyber Security', 'Networking',\n", "       'Software Development', 'Programming Skills', 'Project Management',\n", "       'Computer Forensics Fundamentals', 'Technical Communication', 'AI ML',\n", "       'Software Engineering', 'Business Analysis', 'Data Science',\n", "       'Troubleshooting skills', 'Role'],\n", "      dtype='object')"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "IMhRr5y9w4wg"}, "outputs": [], "source": ["# removing columns list\n", "dropping = ['Project Management',\n", "       'Computer Forensics Fundamentals', 'Technical Communication','Troubleshooting skills',]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "wx2ZK0Vwl1j-"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lTjt-f2Ll1ft"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "TXB6JFyml1dn"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iQjtnP7-l1au"}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "cndPLSrfvHvx"}, "outputs": [], "source": ["skill_mapping = {\n", "    'No Experience': 0,\n", "    'Poor': 1,\n", "    'Novice': 2,\n", "    'Beginner': 3,\n", "    'Entry-level': 4,\n", "    'Average': 5,\n", "    'Intermediate': 6,\n", "    'Advanced': 7,\n", "    'Expert': 8,\n", "    'Professional': 9,\n", "    'Excellent': 10,\n", "    'N/A': None,\n", "    'Not Interested': None,\n", "    'None': None,\n", "    '': None\n", "}"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 0}