/* Results page styling */
.results-header {
    text-align: center;
    margin: 2rem 0;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1), rgba(224, 176, 255, 0.1));
    border-radius: 15px;
}

.results-header h1 {
    color: #2D3748;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.results-header p {
    color: #4A5568;
    font-size: 1.2rem;
}

.career-card {
    background: linear-gradient(135deg, #6c5ce7, #a78bfa);
    border-radius: 15px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.career-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.career-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.career-content h2 {
    color: white;
    margin: 0;
    font-size: 1.5rem;
}

.roadmap-button {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    transition: background 0.3s ease;
    font-weight: 500;
    display: inline-block;
}

.roadmap-button:hover {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

.loading-container {
    text-align: center;
    padding: 3rem;
}

.loading-spinner {
    border: 4px solid rgba(108, 92, 231, 0.1);
    border-radius: 50%;
    border-top: 4px solid #6c5ce7;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .career-content {
        flex-direction: column;
        text-align: center;
    }
    
    .roadmap-button {
        width: 100%;
        text-align: center;
    }
}