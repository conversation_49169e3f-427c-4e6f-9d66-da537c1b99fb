# Realistic Dataset Analysis
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Load the realistic dataset
df = pd.read_csv('dataset/realistic_dataset.csv')

# Basic information
print(f"Dataset shape: {df.shape}")
print("\nRole distribution:")
role_counts = df['Role'].value_counts()
print(role_counts)

# Visualize role distribution
plt.figure(figsize=(12, 6))
role_counts.plot(kind='bar')
plt.title('Distribution of Roles in Realistic Dataset')
plt.xlabel('Role')
plt.ylabel('Count')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# Check for missing values
missing_values = df.isnull().sum()
print("\nMissing values per column:")
print(missing_values)

# Convert skill levels to numeric values for analysis
skill_mapping = {
    'No Experience': 0,
    'Poor': 1,
    'Novice': 2,
    'Beginner': 3,
    'Entry-level': 4,
    'Average': 5,
    'Intermediate': 6,
    'Advanced': 7,
    'Expert': 8,
    'Professional': 9,
    'Excellent': 10,
    'N/A': None,
    'Not Interested': None,
    'None': None,
    '': None
}

# Create a copy for numeric analysis
df_numeric = df.copy()

# Convert all skill columns to numeric
for col in df.columns:
    if col != 'Role':
        df_numeric[col] = df_numeric[col].map(skill_mapping)

# Calculate average skill level by role
skill_cols = [col for col in df.columns if col != 'Role']
role_skill_avg = df_numeric.groupby('Role')[skill_cols].mean()

# Visualize average skill levels by role
plt.figure(figsize=(14, 8))
sns.heatmap(role_skill_avg, cmap='YlGnBu', annot=True, fmt='.1f', linewidths=0.5)
plt.title('Average Skill Levels by Role')
plt.tight_layout()
plt.show()

# Identify key skills for each role
key_skills = {}
for role in df['Role'].unique():
    role_data = df_numeric[df_numeric['Role'] == role]
    avg_skills = role_data[skill_cols].mean()
    key_skills[role] = avg_skills.nlargest(3).index.tolist()

print("\nTop 3 skills for each role:")
for role, skills in key_skills.items():
    print(f"{role}: {', '.join(skills)}")

# Calculate correlation matrix between skills
corr_matrix = df_numeric[skill_cols].corr()

# Plot correlation heatmap
plt.figure(figsize=(12, 10))
sns.heatmap(corr_matrix, annot=False, cmap='coolwarm', linewidths=0.5)
plt.title('Correlation Between Skills')
plt.tight_layout()
plt.show()

# Class imbalance analysis
imbalance_ratio = role_counts.max() / role_counts.min()
print(f"\nClass imbalance ratio (max/min): {imbalance_ratio:.2f}")

# Visualize skill distribution for top 3 roles
top_3_roles = role_counts.nlargest(3).index
plt.figure(figsize=(15, 10))

for i, skill in enumerate(skill_cols[:6]):  # Show first 6 skills
    plt.subplot(2, 3, i+1)
    for role in top_3_roles:
        role_data = df_numeric[df_numeric['Role'] == role]
        sns.kdeplot(role_data[skill].dropna(), label=role)
    plt.title(f'Distribution of {skill}')
    plt.legend()

plt.tight_layout()
plt.show()