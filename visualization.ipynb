{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Dataset Visualization\n", "This notebook visualizes the cleaned dataset for role prediction."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Load the dataset\n", "df = pd.read_csv('documents/cleaned_dataset.csv')\n", "print(f\"Dataset shape: {df.shape}\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Count the number of each role\n", "role_counts = df['Role'].value_counts()\n", "print(f\"Number of unique roles: {len(role_counts)}\")\n", "\n", "# Plot the distribution of roles\n", "plt.figure(figsize=(12, 8))\n", "role_counts.plot(kind='bar')\n", "plt.title('Distribution of Roles')\n", "plt.xlabel('Role')\n", "plt.ylabel('Count')\n", "plt.xticks(rotation=90)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a heatmap of skills correlation\n", "# First, convert skill levels to numeric values\n", "skill_mapping = {\n", "    'No Experience': 0,\n", "    'Poor': 1,\n", "    'Novice': 2,\n", "    'Beginner': 3,\n", "    'Entry-level': 4,\n", "    'Average': 5,\n", "    'Intermediate': 6,\n", "    'Advanced': 7,\n", "    'Expert': 8,\n", "    'Professional': 9,\n", "    'Excellent': 10,\n", "    'N/A': None,\n", "    'Not Interested': None,\n", "    'None': None,\n", "    '': None\n", "}\n", "\n", "# Create a copy to avoid modifying the original\n", "df_numeric = df.copy()\n", "\n", "# Convert all skill columns to numeric\n", "for col in df.columns[:-1]:  # All columns except 'Role'\n", "    df_numeric[col] = df_numeric[col].map(skill_mapping)\n", "\n", "# Calculate correlation matrix\n", "corr_matrix = df_numeric.iloc[:, :-1].corr()\n", "\n", "# Plot heatmap\n", "plt.figure(figsize=(14, 12))\n", "sns.heatmap(corr_matrix, annot=False, cmap='coolwarm', linewidths=0.5)\n", "plt.title('Correlation Between Skills')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add a pie chart for top 10 roles\n", "top_10_roles = role_counts.head(10)\n", "plt.figure(figsize=(10, 10))\n", "plt.pie(top_10_roles, labels=top_10_roles.index, autopct='%1.1f%%', startangle=90)\n", "plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle\n", "plt.title('Top 10 Roles Distribution')\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 4}