{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["Hello this is just introduction of dataset."], "metadata": {"id": "6HDhKu6Yt93p"}}, {"cell_type": "code", "execution_count": 46, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "qBayMUytt4c7", "outputId": "996da51a-20a1-48a7-e54b-17524a4cc110", "collapsed": true}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   9  1  1.1  1.2  1.3  1.4  1.5  1.6  1.7  1.8  1.9  1.10  1.11  1.12  1.13  \\\n", "0  9  2    1    1    1    1    1    1    1    1    1     1     1     1     1   \n", "1  9  3    1    1    1    1    1    1    1    1    1     1     1     1     1   \n", "2  9  5    1    1    1    1    1    1    1    1    1     1     1     1     1   \n", "3  9  6    1    1    1    1    1    1    1    1    1     1     1     1     1   \n", "4  9  7    1    1    1    1    1    1    1    1    1     1     1     1     1   \n", "\n", "   1.14  1.15  Database Administrator  \n", "0     1     1  Database Administrator  \n", "1     1     1  Database Administrator  \n", "2     1     1  Database Administrator  \n", "3     1     1  Database Administrator  \n", "4     1     1  Database Administrator  "], "text/html": ["\n", "  <div id=\"df-023d5924-505d-4d31-b33b-188f1d512606\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>9</th>\n", "      <th>1</th>\n", "      <th>1.1</th>\n", "      <th>1.2</th>\n", "      <th>1.3</th>\n", "      <th>1.4</th>\n", "      <th>1.5</th>\n", "      <th>1.6</th>\n", "      <th>1.7</th>\n", "      <th>1.8</th>\n", "      <th>1.9</th>\n", "      <th>1.10</th>\n", "      <th>1.11</th>\n", "      <th>1.12</th>\n", "      <th>1.13</th>\n", "      <th>1.14</th>\n", "      <th>1.15</th>\n", "      <th>Database Administrator</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>9</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>9</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-023d5924-505d-4d31-b33b-188f1d512606')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-023d5924-505d-4d31-b33b-188f1d512606 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-023d5924-505d-4d31-b33b-188f1d512606');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-2c0bfe8b-6f82-48cc-a56d-6dd7a1accc8b\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-2c0bfe8b-6f82-48cc-a56d-6dd7a1accc8b')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-2c0bfe8b-6f82-48cc-a56d-6dd7a1accc8b button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 9179,\n  \"fields\": [\n    {\n      \"column\": \"9\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          9,\n          1,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          2,\n          3,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.1\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.2\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.3\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.4\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.5\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.6\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.7\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.8\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.9\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.10\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.11\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.12\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.13\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.14\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"1.15\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Database Administrator\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 17,\n        \"samples\": [\n          \"Database Administrator\",\n          \"Hardware Engineer\",\n          \"Software Developer\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 46}], "source": ["# prompt: create a code for read csv file\n", "\n", "import pandas as pd\n", "\n", "# Assuming your CSV file is named 'your_file.csv'\n", "\n", "df = pd.read_csv('dataset9000.data')\n", "df.head()# Prints the first few rows of the DataFrame\n"]}, {"cell_type": "code", "source": ["# prompt: create a code for read csv file\n", "\n", "import pandas as pd\n", "\n", "# Assuming your CSV file is named 'your_file.csv'\n", "\n", "dfcsv = pd.read_csv('dataset9000.csv')\n", "dfcsv.head()# Prints the first few rows of the DataFrame"], "metadata": {"id": "HTmT15g4yCIS", "collapsed": true}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["for col in dfcsv.columns:\n", "  print(f\"Unique values for column '{col}': {dfcsv[col].unique()}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "yUeu4k5QlqrD", "outputId": "cf9dcfaa-9588-432d-ad3a-ed27a4ad7b17"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Unique values for column 'Database Fundamentals': ['Professional' 'Not Interested' 'Poor' 'Beginner' 'Average'\n", " 'Intermediate' 'Excellent']\n", "Unique values for column 'Computer Architecture': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Distributed Computing Systems': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Cyber Security': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Networking': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Software Development': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Programming Skills': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Project Management': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Computer Forensics Fundamentals': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Technical Communication': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'AI ML': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Software Engineering': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Business Analysis': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Communication skills': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Data Science': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Troubleshooting skills': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " 'Professional']\n", "Unique values for column 'Graphics Designing': ['Not Interested' 'Poor' 'Beginner' 'Average' 'Intermediate' 'Excellent'\n", " nan 'Professional']\n", "Unique values for column 'Role': ['Database Administrator' 'Hardware Engineer'\n", " 'Application Support Engineer' 'Cyber Security Specialist'\n", " 'Networking Engineer' 'Software Developer' 'API Specialist'\n", " 'Project Manager' 'Information Security Specialist' 'Technical Writer'\n", " 'AI ML Specialist' 'Software tester' 'Business Analyst'\n", " 'Customer Service Executive' 'Data Scientist' 'Helpdesk Engineer'\n", " 'Graphics Designer']\n"]}]}, {"cell_type": "markdown", "source": ["Checking unique values .\n", "and now we can change scores from 0 to 7"], "metadata": {"id": "llMoW9-DQJQ4"}}, {"cell_type": "code", "source": ["for col in df.columns:\n", "  print(f\"Unique values for column '{col}': {df[col].unique()}\")"], "metadata": {"collapsed": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "PHakt5yAQExQ", "outputId": "eb9f556f-2a36-48a1-931d-d97b20927702"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Unique values for column '9': [9 1 2 3 5 6 7]\n", "Unique values for column '1': [2 3 5 6 7 1 9]\n", "Unique values for column '1.1': [1 2 3 5 6 7 9]\n", "Unique values for column '1.2': [1 2 3 5 6 7 9]\n", "Unique values for column '1.3': [1 2 3 5 6 7 9]\n", "Unique values for column '1.4': [1 2 3 5 6 7 9]\n", "Unique values for column '1.5': [1 2 3 5 6 7 9]\n", "Unique values for column '1.6': [1 2 3 5 6 7 9]\n", "Unique values for column '1.7': [1 2 3 5 6 7 9]\n", "Unique values for column '1.8': [1 2 3 5 6 7 9]\n", "Unique values for column '1.9': [1 2 3 5 6 7 9]\n", "Unique values for column '1.10': [1 2 3 5 6 7 9]\n", "Unique values for column '1.11': [1 2 3 5 6 7 9]\n", "Unique values for column '1.12': [1 2 3 5 6 7 9]\n", "Unique values for column '1.13': [1 2 3 5 6 7 9]\n", "Unique values for column '1.14': [1 2 3 5 6 7 9]\n", "Unique values for column '1.15': [1 2 3 5 6 7 9]\n", "Unique values for column 'Database Administrator': ['Database Administrator' 'Hardware Engineer'\n", " 'Application Support Engineer' 'Cyber Security Specialist'\n", " 'Networking Engineer' 'Software Developer' 'API Integration Specialist'\n", " 'Project Manager' 'Information Security Specialist' 'Technical Writer'\n", " 'AI ML Specialist' 'Software Tester' 'Business Analyst'\n", " 'Customer Service Executive' 'Data Scientist' 'Helpdesk Engineer'\n", " 'Graphics Designer']\n"]}]}, {"cell_type": "code", "source": ["df['9'].unique()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pYGd8Xnyl93a", "outputId": "d9080184-732c-48c0-f538-a0c05f4d99db"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["array([9, 1, 2, 3, 5, 6, 7])"]}, "metadata": {}, "execution_count": 6}]}, {"cell_type": "code", "source": ["# assigning column names to data file\n", "df.columns= [\"Database Fundamentals\",\"Computer Architecture\",\"Distributed Computing Systems\",\n", "\"Cyber-Security\",\"Networking\",\"Development\",\"Programming Skills\",\"Project Management\",\n", "\"Computer Forensics Fundamentals\",\"Technical Communication\",\"AI ML\",\"Software Engineering\",\"Business Analysis\",\n", "\"Communication skills\",\"Data Science\",\"Troubleshooting-skills\",\"Graphics Designing\",\"Roles\"]"], "metadata": {"id": "ZESNNYp0Rba8"}, "execution_count": 48, "outputs": []}, {"cell_type": "code", "source": ["df.describe()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 355}, "id": "45OtUnpSumb_", "outputId": "9b042b3b-3fbc-4f58-eed5-c8fb421a802f", "collapsed": true}, "execution_count": 21, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["       Database Fundamentals  Computer Architecture  \\\n", "count            9179.000000            9179.000000   \n", "mean                4.292951               4.293823   \n", "std                 2.403221               2.403477   \n", "min                 1.000000               1.000000   \n", "25%                 2.000000               2.000000   \n", "50%                 5.000000               5.000000   \n", "75%                 6.000000               6.000000   \n", "max                 9.000000               9.000000   \n", "\n", "       Distributed Computing Systems  Cyber-Security   Networking  \\\n", "count                    9179.000000     9179.000000  9179.000000   \n", "mean                        4.293823        4.293823     4.293823   \n", "std                         2.403477        2.403477     2.403477   \n", "min                         1.000000        1.000000     1.000000   \n", "25%                         2.000000        2.000000     2.000000   \n", "50%                         5.000000        5.000000     5.000000   \n", "75%                         6.000000        6.000000     6.000000   \n", "max                         9.000000        9.000000     9.000000   \n", "\n", "       Development  Programming Skills  Project Management  \\\n", "count  9179.000000         9179.000000         9179.000000   \n", "mean      4.293823            4.293823            4.293823   \n", "std       2.403477            2.403477            2.403477   \n", "min       1.000000            1.000000            1.000000   \n", "25%       2.000000            2.000000            2.000000   \n", "50%       5.000000            5.000000            5.000000   \n", "75%       6.000000            6.000000            6.000000   \n", "max       9.000000            9.000000            9.000000   \n", "\n", "       Computer Forensics Fundamentals  Technical Communication        AI ML  \\\n", "count                      9179.000000              9179.000000  9179.000000   \n", "mean                          4.293823                 4.293823     4.293823   \n", "std                           2.403477                 2.403477     2.403477   \n", "min                           1.000000                 1.000000     1.000000   \n", "25%                           2.000000                 2.000000     2.000000   \n", "50%                           5.000000                 5.000000     5.000000   \n", "75%                           6.000000                 6.000000     6.000000   \n", "max                           9.000000                 9.000000     9.000000   \n", "\n", "       Software Engineering  Business Analysis  Communication skills  \\\n", "count           9179.000000        9179.000000           9179.000000   \n", "mean               4.293823           4.293823              4.293823   \n", "std                2.403477           2.403477              2.403477   \n", "min                1.000000           1.000000              1.000000   \n", "25%                2.000000           2.000000              2.000000   \n", "50%                5.000000           5.000000              5.000000   \n", "75%                6.000000           6.000000              6.000000   \n", "max                9.000000           9.000000              9.000000   \n", "\n", "       Data Science  Troubleshooting-skills  Graphics Designing  \n", "count   9179.000000             9179.000000         9179.000000  \n", "mean       4.294477                4.294259            4.294477  \n", "std        2.403397                2.403242            2.403397  \n", "min        1.000000                1.000000            1.000000  \n", "25%        2.000000                2.000000            2.000000  \n", "50%        5.000000                5.000000            5.000000  \n", "75%        6.000000                6.000000            6.000000  \n", "max        9.000000                9.000000            9.000000  "], "text/html": ["\n", "  <div id=\"df-411b9534-763d-4e1e-8373-c9f662b3c564\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Database Fundamentals</th>\n", "      <th>Computer Architecture</th>\n", "      <th>Distributed Computing Systems</th>\n", "      <th>Cyber-Security</th>\n", "      <th>Networking</th>\n", "      <th>Development</th>\n", "      <th>Programming Skills</th>\n", "      <th>Project Management</th>\n", "      <th>Computer Forensics Fundamentals</th>\n", "      <th>Technical Communication</th>\n", "      <th>AI ML</th>\n", "      <th>Software Engineering</th>\n", "      <th>Business Analysis</th>\n", "      <th>Communication skills</th>\n", "      <th>Data Science</th>\n", "      <th>Troubleshooting-skills</th>\n", "      <th>Graphics Designing</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "      <td>9179.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>4.292951</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.293823</td>\n", "      <td>4.294477</td>\n", "      <td>4.294259</td>\n", "      <td>4.294477</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>2.403221</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403477</td>\n", "      <td>2.403397</td>\n", "      <td>2.403242</td>\n", "      <td>2.403397</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "      <td>5.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "      <td>6.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "      <td>9.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-411b9534-763d-4e1e-8373-c9f662b3c564')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-411b9534-763d-4e1e-8373-c9f662b3c564 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-411b9534-763d-4e1e-8373-c9f662b3c564');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-ff9b6e08-6f36-474a-aa58-2fea1ac2c3c0\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-ff9b6e08-6f36-474a-aa58-2fea1ac2c3c0')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-ff9b6e08-6f36-474a-aa58-2fea1ac2c3c0 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 8,\n  \"fields\": [\n    {\n      \"column\": \"Database Fundamentals\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.767684933864,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.292951301884737,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Computer Architecture\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Distributed Computing Systems\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Cyber-Security\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Networking\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Development\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Programming Skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Project Management\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Computer Forensics Fundamentals\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Technical Communication\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"AI ML\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Software Engineering\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Business Analysis\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Communication skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676279510697,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.293822856520318,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Data Science\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7675989849927,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.294476522497004,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Troubleshooting-skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7676178131674,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.294258633838108,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Graphics Designing\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3243.7675989849927,\n        \"min\": 1.0,\n        \"max\": 9179.0,\n        \"num_unique_values\": 8,\n        \"samples\": [\n          4.294476522497004,\n          5.0,\n          9179.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 21}]}, {"cell_type": "code", "source": ["# checking entries of each jobrole\n", "print(df.groupby(['Roles']).sum())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "41Jn96f9vPIf", "outputId": "88baa544-bd10-477a-ef00-359788cd978a"}, "execution_count": 49, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["                                 Database Fundamentals  Computer Architecture  \\\n", "Roles                                                                           \n", "AI ML Specialist                                  2160                   2160   \n", "API Integration Specialist                        2160                   2160   \n", "Application Support Engineer                      2160                   2160   \n", "Business Analyst                                  2160                   2160   \n", "Customer Service Executive                        2160                   2160   \n", "Cyber Security Specialist                         2160                   2160   \n", "Data Scientist                                    2160                   2160   \n", "Database Administrator                            4851                   2159   \n", "Graphics Designer                                 2154                   2154   \n", "Hardware Engineer                                 2160                   4860   \n", "Helpdesk Engineer                                 2160                   2160   \n", "Information Security Specialist                   2160                   2160   \n", "Networking Engineer                               2160                   2160   \n", "Project Manager                                   2160                   2160   \n", "Software Developer                                2160                   2160   \n", "Software Tester                                   2160                   2160   \n", "Technical Writer                                  2160                   2160   \n", "\n", "                                 Distributed Computing Systems  \\\n", "Roles                                                            \n", "AI ML Specialist                                          2160   \n", "API Integration Specialist                                2160   \n", "Application Support Engineer                              4860   \n", "Business Analyst                                          2160   \n", "Customer Service Executive                                2160   \n", "Cyber Security Specialist                                 2160   \n", "Data Scientist                                            2160   \n", "Database Administrator                                    2159   \n", "Graphics Designer                                         2154   \n", "Hardware Engineer                                         2160   \n", "Helpdesk Engineer                                         2160   \n", "Information Security Specialist                           2160   \n", "Networking Engineer                                       2160   \n", "Project Manager                                           2160   \n", "Software Developer                                        2160   \n", "Software Tester                                           2160   \n", "Technical Writer                                          2160   \n", "\n", "                                 Cyber-Security  Networking  Development  \\\n", "Roles                                                                      \n", "AI ML Specialist                           2160        2160         2160   \n", "API Integration Specialist                 2160        2160         2160   \n", "Application Support Engineer               2160        2160         2160   \n", "Business Analyst                           2160        2160         2160   \n", "Customer Service Executive                 2160        2160         2160   \n", "Cyber Security Specialist                  4860        2160         2160   \n", "Data Scientist                             2160        2160         2160   \n", "Database Administrator                     2159        2159         2159   \n", "Graphics Designer                          2154        2154         2154   \n", "Hardware Engineer                          2160        2160         2160   \n", "Helpdesk Engineer                          2160        2160         2160   \n", "Information Security Specialist            2160        2160         2160   \n", "Networking Engineer                        2160        4860         2160   \n", "Project Manager                            2160        2160         2160   \n", "Software Developer                         2160        2160         4860   \n", "Software Tester                            2160        2160         2160   \n", "Technical Writer                           2160        2160         2160   \n", "\n", "                                 Programming Skills  Project Management  \\\n", "Roles                                                                     \n", "AI ML Specialist                               2160                2160   \n", "API Integration Specialist                     4860                2160   \n", "Application Support Engineer                   2160                2160   \n", "Business Analyst                               2160                2160   \n", "Customer Service Executive                     2160                2160   \n", "Cyber Security Specialist                      2160                2160   \n", "Data Scientist                                 2160                2160   \n", "Database Administrator                         2159                2159   \n", "Graphics Designer                              2154                2154   \n", "Hardware Engineer                              2160                2160   \n", "Helpdesk Engineer                              2160                2160   \n", "Information Security Specialist                2160                2160   \n", "Networking Engineer                            2160                2160   \n", "Project Manager                                2160                4860   \n", "Software Developer                             2160                2160   \n", "Software Tester                                2160                2160   \n", "Technical Writer                               2160                2160   \n", "\n", "                                 Computer Forensics Fundamentals  \\\n", "Roles                                                              \n", "AI ML Specialist                                            2160   \n", "API Integration Specialist                                  2160   \n", "Application Support Engineer                                2160   \n", "Business Analyst                                            2160   \n", "Customer Service Executive                                  2160   \n", "Cyber Security Specialist                                   2160   \n", "Data Scientist                                              2160   \n", "Database Administrator                                      2159   \n", "Graphics Designer                                           2154   \n", "Hardware Engineer                                           2160   \n", "Helpdesk Engineer                                           2160   \n", "Information Security Specialist                             4860   \n", "Networking Engineer                                         2160   \n", "Project Manager                                             2160   \n", "Software Developer                                          2160   \n", "Software Tester                                             2160   \n", "Technical Writer                                            2160   \n", "\n", "                                 Technical Communication  AI ML  \\\n", "Roles                                                             \n", "AI ML Specialist                                    2160   4860   \n", "API Integration Specialist                          2160   2160   \n", "Application Support Engineer                        2160   2160   \n", "Business Analyst                                    2160   2160   \n", "Customer Service Executive                          2160   2160   \n", "Cyber Security Specialist                           2160   2160   \n", "Data Scientist                                      2160   2160   \n", "Database Administrator                              2159   2159   \n", "Graphics Designer                                   2154   2154   \n", "Hardware Engineer                                   2160   2160   \n", "Helpdesk Engineer                                   2160   2160   \n", "Information Security Specialist                     2160   2160   \n", "Networking Engineer                                 2160   2160   \n", "Project Manager                                     2160   2160   \n", "Software Developer                                  2160   2160   \n", "Software Tester                                     2160   2160   \n", "Technical Writer                                    4860   2160   \n", "\n", "                                 Software Engineering  Business Analysis  \\\n", "Roles                                                                      \n", "AI ML Specialist                                 2160               2160   \n", "API Integration Specialist                       2160               2160   \n", "Application Support Engineer                     2160               2160   \n", "Business Analyst                                 2160               4860   \n", "Customer Service Executive                       2160               2160   \n", "Cyber Security Specialist                        2160               2160   \n", "Data Scientist                                   2160               2160   \n", "Database Administrator                           2159               2159   \n", "Graphics Designer                                2154               2154   \n", "Hardware Engineer                                2160               2160   \n", "Helpdesk Engineer                                2160               2160   \n", "Information Security Specialist                  2160               2160   \n", "Networking Engineer                              2160               2160   \n", "Project Manager                                  2160               2160   \n", "Software Developer                               2160               2160   \n", "Software Tester                                  4860               2160   \n", "Technical Writer                                 2160               2160   \n", "\n", "                                 Communication skills  Data Science  \\\n", "Roles                                                                 \n", "AI ML Specialist                                 2160          2160   \n", "API Integration Specialist                       2160          2160   \n", "Application Support Engineer                     2160          2160   \n", "Business Analyst                                 2160          2160   \n", "Customer Service Executive                       4860          2160   \n", "Cyber Security Specialist                        2160          2160   \n", "Data Scientist                                   2160          4860   \n", "Database Administrator                           2159          2159   \n", "Graphics Designer                                2154          2160   \n", "Hardware Engineer                                2160          2160   \n", "Helpdesk Engineer                                2160          2160   \n", "Information Security Specialist                  2160          2160   \n", "Networking Engineer                              2160          2160   \n", "Project Manager                                  2160          2160   \n", "Software Developer                               2160          2160   \n", "Software Tester                                  2160          2160   \n", "Technical Writer                                 2160          2160   \n", "\n", "                                 Troubleshooting-skills  Graphics Designing  \n", "Roles                                                                        \n", "AI ML Specialist                                   2160                2160  \n", "API Integration Specialist                         2160                2160  \n", "Application Support Engineer                       2160                2160  \n", "Business Analyst                                   2160                2160  \n", "Customer Service Executive                         2160                2160  \n", "Cyber Security Specialist                          2160                2160  \n", "Data Scientist                                     2160                2160  \n", "Database Administrator                             2159                2159  \n", "Graphics Designer                                  2158                4860  \n", "Hardware Engineer                                  2160                2160  \n", "Helpdesk Engineer                                  4860                2160  \n", "Information Security Specialist                    2160                2160  \n", "Networking Engineer                                2160                2160  \n", "Project Manager                                    2160                2160  \n", "Software Developer                                 2160                2160  \n", "Software Tester                                    2160                2160  \n", "Technical Writer                                   2160                2160  \n"]}]}, {"cell_type": "code", "source": ["# chceking null values\n", "df.isna().sum()"], "metadata": {"id": "Mw4issKGvWvV", "outputId": "16416a96-ef89-4313-97f9-541d2734b026", "colab": {"base_uri": "https://localhost:8080/", "height": 649}, "collapsed": true}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["9                         0\n", "1                         0\n", "1.1                       0\n", "1.2                       0\n", "1.3                       0\n", "1.4                       0\n", "1.5                       0\n", "1.6                       0\n", "1.7                       0\n", "1.8                       0\n", "1.9                       0\n", "1.10                      0\n", "1.11                      0\n", "1.12                      0\n", "1.13                      0\n", "1.14                      0\n", "1.15                      0\n", "Database Administrator    0\n", "dtype: int64"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.1</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.2</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.3</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.4</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.5</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.6</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.7</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.8</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.9</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.10</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.11</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.12</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.13</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.14</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1.15</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Database Administrator</th>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div><br><label><b>dtype:</b> int64</label>"]}, "metadata": {}, "execution_count": 4}]}, {"cell_type": "code", "source": ["# dfcsv.columns"], "metadata": {"id": "IOGvqpWGt5qX", "collapsed": true}, "execution_count": 13, "outputs": []}, {"cell_type": "markdown", "source": ["Changing values (5:4 , 6:5, 7:6 , 9:7)\n", "WE need to do it at the time of prediction."], "metadata": {"id": "SCppOa_ySSVQ"}}, {"cell_type": "code", "source": ["# df=df.replace({5:4 , 6:5, 7:6 , 9:7})\n", "# df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 348}, "collapsed": true, "id": "39gOxi-vSQmq", "outputId": "cebf014c-5a41-4958-cad9-84db24aa1216"}, "execution_count": 22, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   Database Fundamentals  Computer Architecture  \\\n", "0                      7                      2   \n", "1                      7                      3   \n", "2                      7                      4   \n", "3                      7                      5   \n", "4                      7                      6   \n", "\n", "   Distributed Computing Systems  Cyber-Security  Networking  Development  \\\n", "0                              1               1           1            1   \n", "1                              1               1           1            1   \n", "2                              1               1           1            1   \n", "3                              1               1           1            1   \n", "4                              1               1           1            1   \n", "\n", "   Programming Skills  Project Management  Computer Forensics Fundamentals  \\\n", "0                   1                   1                                1   \n", "1                   1                   1                                1   \n", "2                   1                   1                                1   \n", "3                   1                   1                                1   \n", "4                   1                   1                                1   \n", "\n", "   Technical Communication  AI ML  Software Engineering  Business Analysis  \\\n", "0                        1      1                     1                  1   \n", "1                        1      1                     1                  1   \n", "2                        1      1                     1                  1   \n", "3                        1      1                     1                  1   \n", "4                        1      1                     1                  1   \n", "\n", "   Communication skills  Data Science  Troubleshooting-skills  \\\n", "0                     1             1                       1   \n", "1                     1             1                       1   \n", "2                     1             1                       1   \n", "3                     1             1                       1   \n", "4                     1             1                       1   \n", "\n", "   Graphics Designing                   Roles  \n", "0                   1  Database Administrator  \n", "1                   1  Database Administrator  \n", "2                   1  Database Administrator  \n", "3                   1  Database Administrator  \n", "4                   1  Database Administrator  "], "text/html": ["\n", "  <div id=\"df-1dd55488-0100-4485-b39c-f007255b1d93\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Database Fundamentals</th>\n", "      <th>Computer Architecture</th>\n", "      <th>Distributed Computing Systems</th>\n", "      <th>Cyber-Security</th>\n", "      <th>Networking</th>\n", "      <th>Development</th>\n", "      <th>Programming Skills</th>\n", "      <th>Project Management</th>\n", "      <th>Computer Forensics Fundamentals</th>\n", "      <th>Technical Communication</th>\n", "      <th>AI ML</th>\n", "      <th>Software Engineering</th>\n", "      <th>Business Analysis</th>\n", "      <th>Communication skills</th>\n", "      <th>Data Science</th>\n", "      <th>Troubleshooting-skills</th>\n", "      <th>Graphics Designing</th>\n", "      <th>Roles</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>7</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-1dd55488-0100-4485-b39c-f007255b1d93')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-1dd55488-0100-4485-b39c-f007255b1d93 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-1dd55488-0100-4485-b39c-f007255b1d93');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-ba1f7b5b-d90a-4f91-b499-3de58dcb740b\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-ba1f7b5b-d90a-4f91-b499-3de58dcb740b')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-ba1f7b5b-d90a-4f91-b499-3de58dcb740b button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 9179,\n  \"fields\": [\n    {\n      \"column\": \"Database Fundamentals\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          7,\n          1,\n          5\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Computer Architecture\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          2,\n          3,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Distributed Computing Systems\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Cyber-Security\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Networking\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Development\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Programming Skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Project Management\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Computer Forensics Fundamentals\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Technical Communication\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"AI ML\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Software Engineering\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Business Analysis\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Communication skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Data Science\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Troubleshooting-skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Graphics Designing\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1,\n        \"min\": 1,\n        \"max\": 7,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Roles\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 17,\n        \"samples\": [\n          \"Database Administrator\",\n          \"Hardware Engineer\",\n          \"Software Developer\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 22}]}, {"cell_type": "code", "source": ["from sklearn import model_selection\n", "from sklearn.ensemble import BaggingClassifier\n", "from sklearn.tree import DecisionTreeClassifier\n", "import pandas as pd\n", "import numpy as np\n", "import pickle\n", "\n", "# load the data\n", "dataset = pd.read_csv('dataset9000.data', header = None)\n", "print(dataset.head())\n", "X=np.array(dataset.iloc[:, 0:17])\n", "print(X)\n", "Y = np.array(dataset.iloc[:, 17])\n", "print(Y)\n", "dataset.columns= [\"Database Fundamentals\",\"Computer Architecture\",\"Distributed Computing Systems\",\n", "\"Cyber-Security\",\"Networking\",\"Development\",\"Programming Skills\",\"Project Management\",\n", "\"Computer Forensics Fundamentals\",\"Technical Communication\",\"AI ML\",\"Software Engineering\",\"Business Analysis\",\n", "\"Communication skills\",\"Data Science\",\"Troubleshooting-skills\",\"Graphics Designing\",\"Roles\"]\n", "dataset.dropna(how ='all', inplace = True)\n", "\n", "\n", "seed =5\n", "kfold = model_selection.KFold(n_splits = 15,shuffle=True,random_state = seed)\n", "\n", "# initialize the base classifier\n", "base_cls = DecisionTreeClassifier()\n", "\n", "# no. of base classifier\n", "num_trees = 70\n", "\n", "# bagging classifier\n", "model = BaggingClassifier(estimator = base_cls,\n", "                          n_estimators = num_trees,\n", "                          random_state = seed)\n", "\n", "results = model_selection.cross_val_score(model, X, Y, cv = kfold)\n", "print(\"accuracy :\",results.mean()*100)\n", "\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "IKKl1kthdgOL", "outputId": "b4561048-48be-4b45-e17e-2139a18201d0"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["   0   1   2   3   4   5   6   7   8   9   10  11  12  13  14  15  16  \\\n", "0   9   1   1   1   1   1   1   1   1   1   1   1   1   1   1   1   1   \n", "1   9   2   1   1   1   1   1   1   1   1   1   1   1   1   1   1   1   \n", "2   9   3   1   1   1   1   1   1   1   1   1   1   1   1   1   1   1   \n", "3   9   5   1   1   1   1   1   1   1   1   1   1   1   1   1   1   1   \n", "4   9   6   1   1   1   1   1   1   1   1   1   1   1   1   1   1   1   \n", "\n", "                       17  \n", "0  Database Administrator  \n", "1  Database Administrator  \n", "2  Database Administrator  \n", "3  Database Administrator  \n", "4  Database Administrator  \n", "[[9 1 1 ... 1 1 1]\n", " [9 2 1 ... 1 1 1]\n", " [9 3 1 ... 1 1 1]\n", " ...\n", " [1 1 1 ... 6 6 9]\n", " [1 1 1 ... 7 7 9]\n", " [1 1 1 ... 7 5 9]]\n", "['Database Administrator' 'Database Administrator'\n", " 'Database Administrator' ... 'Graphics Designer' 'Graphics Designer'\n", " 'Graphics Designer']\n", "accuracy : 100.0\n"]}]}, {"cell_type": "markdown", "source": ["#Technical Skills:\n", "Database Fundamentals, Computer Architecture, Distributed Computing Systems, Cyber Security, Networking, Software Development, Programming Skills, Computer Forensics Fundamentals, AI ML, Software Engineering, Data Science, Graphics Designing.\n", "\n", "#Soft Skills:\n", "Project Management, Technical Communication, Business Analysis, Communication Skills, Troubleshooting Skills.\n"], "metadata": {"id": "wloBDZUykCM_"}}, {"cell_type": "markdown", "source": ["#knn Test"], "metadata": {"id": "5cm8usp6A2Oc"}}, {"cell_type": "code", "source": ["import pandas as pd\n", "\n", "# Assuming your CSV file is named 'your_file.csv'\n", "\n", "df = pd.read_csv('dataset9000.data')\n", "df.columns=[\"Database Fundamentals\",\"Computer Architecture\",\"Distributed Computing Systems\",\n", "\"Cyber-Security\",\"Networking\",\"Development\",\"Programming Skills\",\"Project Management\",\n", "\"Computer Forensics Fundamentals\",\"Technical Communication\",\"AI ML\",\"Software Engineering\",\"Business Analysis\",\n", "\"Communication skills\",\"Data Science\",\"Troubleshooting-skills\",\"Graphics Designing\",\"Roles\"]\n", "# df=df.replace({5:4 , 6:5, 7:6 , 9:7})\n", "df.head()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 348}, "collapsed": true, "id": "2vai_BR5BC5W", "outputId": "837c0fdb-d298-4124-fd0e-9bee09f281d0"}, "execution_count": 50, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   Database Fundamentals  Computer Architecture  \\\n", "0                      9                      2   \n", "1                      9                      3   \n", "2                      9                      5   \n", "3                      9                      6   \n", "4                      9                      7   \n", "\n", "   Distributed Computing Systems  Cyber-Security  Networking  Development  \\\n", "0                              1               1           1            1   \n", "1                              1               1           1            1   \n", "2                              1               1           1            1   \n", "3                              1               1           1            1   \n", "4                              1               1           1            1   \n", "\n", "   Programming Skills  Project Management  Computer Forensics Fundamentals  \\\n", "0                   1                   1                                1   \n", "1                   1                   1                                1   \n", "2                   1                   1                                1   \n", "3                   1                   1                                1   \n", "4                   1                   1                                1   \n", "\n", "   Technical Communication  AI ML  Software Engineering  Business Analysis  \\\n", "0                        1      1                     1                  1   \n", "1                        1      1                     1                  1   \n", "2                        1      1                     1                  1   \n", "3                        1      1                     1                  1   \n", "4                        1      1                     1                  1   \n", "\n", "   Communication skills  Data Science  Troubleshooting-skills  \\\n", "0                     1             1                       1   \n", "1                     1             1                       1   \n", "2                     1             1                       1   \n", "3                     1             1                       1   \n", "4                     1             1                       1   \n", "\n", "   Graphics Designing                   Roles  \n", "0                   1  Database Administrator  \n", "1                   1  Database Administrator  \n", "2                   1  Database Administrator  \n", "3                   1  Database Administrator  \n", "4                   1  Database Administrator  "], "text/html": ["\n", "  <div id=\"df-6260924a-7450-4e12-b835-747b61162158\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Database Fundamentals</th>\n", "      <th>Computer Architecture</th>\n", "      <th>Distributed Computing Systems</th>\n", "      <th>Cyber-Security</th>\n", "      <th>Networking</th>\n", "      <th>Development</th>\n", "      <th>Programming Skills</th>\n", "      <th>Project Management</th>\n", "      <th>Computer Forensics Fundamentals</th>\n", "      <th>Technical Communication</th>\n", "      <th>AI ML</th>\n", "      <th>Software Engineering</th>\n", "      <th>Business Analysis</th>\n", "      <th>Communication skills</th>\n", "      <th>Data Science</th>\n", "      <th>Troubleshooting-skills</th>\n", "      <th>Graphics Designing</th>\n", "      <th>Roles</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>9</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>9</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-6260924a-7450-4e12-b835-747b61162158')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-6260924a-7450-4e12-b835-747b61162158 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-6260924a-7450-4e12-b835-747b61162158');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-14677ce2-87d8-4ec3-81f3-6ce5f279babb\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-14677ce2-87d8-4ec3-81f3-6ce5f279babb')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-14677ce2-87d8-4ec3-81f3-6ce5f279babb button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 9179,\n  \"fields\": [\n    {\n      \"column\": \"Database Fundamentals\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          9,\n          1,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Computer Architecture\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          2,\n          3,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Distributed Computing Systems\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Cyber-Security\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Networking\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Development\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Programming Skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Project Management\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Computer Forensics Fundamentals\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Technical Communication\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"AI ML\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Software Engineering\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Business Analysis\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Communication skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Data Science\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Troubleshooting-skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Graphics Designing\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Roles\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 17,\n        \"samples\": [\n          \"Database Administrator\",\n          \"Hardware Engineer\",\n          \"Software Developer\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 50}]}, {"cell_type": "code", "source": ["df.shape"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XGzXWIOTBrls", "outputId": "676c27b9-2ccf-427c-8889-6919799c0de1"}, "execution_count": 25, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["(9179, 18)"]}, "metadata": {}, "execution_count": 25}]}, {"cell_type": "code", "source": ["x=df.iloc[:,:17]\n", "y=df.iloc[:,17]\n", "x.head()\n", "y.head()"], "metadata": {"id": "LgnQBgoCBy3C", "colab": {"base_uri": "https://localhost:8080/", "height": 241}, "collapsed": true, "outputId": "a5583cec-1793-465b-c510-4fb5a8b3bf5e"}, "execution_count": 51, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0    Database Administrator\n", "1    Database Administrator\n", "2    Database Administrator\n", "3    Database Administrator\n", "4    Database Administrator\n", "Name: Roles, dtype: object"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Roles</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div><br><label><b>dtype:</b> object</label>"]}, "metadata": {}, "execution_count": 51}]}, {"cell_type": "code", "source": ["import numpy as np\n", "from sklearn.neighbors import KNeighborsClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, classification_report"], "metadata": {"id": "t80URVGrCtkl"}, "execution_count": 52, "outputs": []}, {"cell_type": "code", "source": ["kn=KNeighborsClassifier(n_neighbors=5)"], "metadata": {"id": "hypWcncy4ZZ3", "collapsed": true}, "execution_count": 53, "outputs": []}, {"cell_type": "code", "source": ["xtrain,xtest,ytrain,ytest = train_test_split(x,y,test_size=0.2,random_state=10)"], "metadata": {"id": "A6NeAKMD5JB6"}, "execution_count": 54, "outputs": []}, {"cell_type": "code", "source": ["# import numpy as np\n", "# cv_scores = cross_val_score(kkn, xtrain, ytrain, cv=5)  # 5-fold cross-validation\n", "# print(f\"Cross-validation scores: {cv_scores}\")\n", "# print(f\"Average cross-validation score: {np.mean(cv_scores)}\")"], "metadata": {"id": "pldaZQdp4vXB"}, "execution_count": 55, "outputs": []}, {"cell_type": "code", "source": ["kn.fit(X=xtrain,y=ytrain)"], "metadata": {"id": "R3GOxraFdxjf", "colab": {"base_uri": "https://localhost:8080/", "height": 80}, "outputId": "b36c81bd-6c75-41e0-aabf-700f171f6159"}, "execution_count": 56, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["KNeighborsClassifier()"], "text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>KNeighborsClassifier()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>KNeighborsClassifier</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.neighbors.KNeighborsClassifier.html\">?<span>Documentation for KNeighborsClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>KNeighborsClassifier()</pre></div> </div></div></div></div>"]}, "metadata": {}, "execution_count": 56}]}, {"cell_type": "code", "source": ["prediction= kn.predict(xtest)\n", "print(\"accuracy=\",accuracy_score(ytest,prediction))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "q5V20f6NGwaM", "outputId": "9c30331e-700b-4922-e0a0-4f5bb40fc221"}, "execution_count": 57, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["accuracy= 0.9754901960784313\n"]}]}, {"cell_type": "markdown", "source": ["#optimal knn model with hyper parameter tuning\n"], "metadata": {"id": "RmrWWzmLUDCD"}}, {"cell_type": "code", "source": ["scores=[]\n", "for i in range(1,15):\n", "  kn=KNeighborsClassifier(n_neighbors=i)\n", "  kn.fit(X=xtrain,y=ytrain)\n", "  prediction= kn.predict(xtest)\n", "  scores.append(accuracy_score(ytest,prediction))"], "metadata": {"id": "-EohTzVydxhV"}, "execution_count": 58, "outputs": []}, {"cell_type": "code", "source": ["scores"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Tg_c2Ox2JMDn", "outputId": "0fbc7fa3-6af8-41ce-91ac-c4517fd5fa43", "collapsed": true}, "execution_count": 59, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[0.9923747276688453,\n", " 0.9705882352941176,\n", " 0.9673202614379085,\n", " 0.9591503267973857,\n", " 0.9754901960784313,\n", " 0.9754901960784313,\n", " 0.9727668845315904,\n", " 0.9700435729847494,\n", " 0.9738562091503268,\n", " 0.9667755991285403,\n", " 0.9711328976034859,\n", " 0.9689542483660131,\n", " 0.9694989106753813,\n", " 0.9635076252723311]"]}, "metadata": {}, "execution_count": 59}]}, {"cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "plt.plot(range(1, 15), scores)\n", "plt.xlabel(\"Number of Neighbors (K)\")\n", "plt.ylabel(\"Accuracy\")\n", "plt.title(\"KNN Accuracy vs. Number of Neighbors\")\n", "plt.show()"], "metadata": {"id": "4ycATiJDdxd9", "colab": {"base_uri": "https://localhost:8080/", "height": 472}, "outputId": "f11121b3-5e26-4bef-a3d7-1f5260f56155"}, "execution_count": 60, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["Avoiding model with 1 neighbor because it can be lead us to overfitting .\n", "so the best neighbors will be 5."], "metadata": {"id": "38LMjJ2gTuz7"}}, {"cell_type": "code", "source": ["from sklearn.model_selection import cross_val_score\n", "from sklearn.metrics import classification_report, confusion_matrix"], "metadata": {"id": "KdZA_FWa2qzd"}, "execution_count": 61, "outputs": []}, {"cell_type": "code", "source": ["kkn=KNeighborsClassifier(n_neighbors=5)\n", "kkn.fit(X=xtrain,y=ytrain)\n", "prediction= kn.predict(xtest)"], "metadata": {"id": "kwAqHwDA4Ixq"}, "execution_count": 63, "outputs": []}, {"cell_type": "code", "source": ["accuracy_score(ytest, prediction)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9HHRRuvkVDA3", "outputId": "4be2b3de-72cd-4df5-a63e-3166f8cb837a"}, "execution_count": 64, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["0.9635076252723311"]}, "metadata": {}, "execution_count": 64}]}, {"cell_type": "code", "source": ["print(classification_report(ytest,prediction))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "OSSUDqqE2xaF", "outputId": "78011ce9-8ebf-4e0c-81db-5c95a853afa5"}, "execution_count": 65, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["                                 precision    recall  f1-score   support\n", "\n", "               AI ML Specialist       0.79      1.00      0.88       102\n", "     API Integration Specialist       0.99      0.97      0.98       119\n", "   Application Support Engineer       0.93      0.98      0.95       104\n", "               Business Analyst       1.00      0.96      0.98        99\n", "     Customer Service Executive       0.93      0.99      0.96       101\n", "      Cyber Security Specialist       0.97      0.95      0.96       113\n", "                 Data Scientist       0.97      0.98      0.98       108\n", "         Database Administrator       0.97      0.93      0.95       121\n", "              Graphics Designer       0.95      0.98      0.97       104\n", "              Hardware Engineer       0.98      0.96      0.97       109\n", "              Helpdesk Engineer       1.00      0.94      0.97       110\n", "Information Security Specialist       0.97      0.99      0.98       107\n", "            Networking Engineer       0.99      0.96      0.97       100\n", "                Project Manager       0.99      0.95      0.97       111\n", "             Software Developer       0.98      0.97      0.98       109\n", "                Software Tester       0.99      0.94      0.96       111\n", "               Technical Writer       1.00      0.94      0.97       108\n", "\n", "                       accuracy                           0.96      1836\n", "                      macro avg       0.97      0.96      0.96      1836\n", "                   weighted avg       0.97      0.96      0.96      1836\n", "\n"]}]}, {"cell_type": "code", "source": ["accuracy = np.mean(prediction == ytest)\n", "print(f\"Accuracy on test set: {accuracy:.4f}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_d4NKxIh2qwh", "outputId": "0fc57ebd-8930-41c9-a9e7-d42e6701499f"}, "execution_count": 66, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Accuracy on test set: 0.9635\n"]}]}, {"cell_type": "code", "source": ["# Confusion Matrix\n", "print(\"Confusion Matrix:\")\n", "print(confusion_matrix(ytest, prediction))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ieStm2za2qt9", "outputId": "fdadea37-fcb9-43b4-c895-192f9c51aa68"}, "execution_count": 67, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Confusion Matrix:\n", "[[102   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  3 116   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0 102   0   0   0   0   1   0   0   0   0   0   0   0   1   0]\n", " [  0   0   1  95   2   1   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0 100   0   1   0   0   0   0   0   0   0   0   0   0]\n", " [  3   0   1   0   1 107   0   0   1   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   1   0 106   0   0   0   0   0   0   0   1   0   0]\n", " [  5   0   1   0   0   0   1 112   0   0   0   0   0   1   1   0   0]\n", " [  1   0   0   0   1   0   0   0 102   0   0   0   0   0   0   0   0]\n", " [  1   1   0   0   0   1   0   0   0 105   0   1   0   0   0   0   0]\n", " [  3   0   1   0   0   0   1   0   2   0 103   0   0   0   0   0   0]\n", " [  0   0   1   0   0   0   0   0   0   0   0 106   0   0   0   0   0]\n", " [  1   0   0   0   0   1   0   0   1   1   0   0  96   0   0   0   0]\n", " [  2   0   1   0   0   0   0   1   1   0   0   1   0 105   0   0   0]\n", " [  0   0   1   0   2   0   0   0   0   0   0   0   0   0 106   0   0]\n", " [  5   0   1   0   0   0   0   0   0   0   0   0   1   0   0 104   0]\n", " [  3   0   0   0   0   0   0   1   0   1   0   1   0   0   0   0 102]]\n"]}]}, {"cell_type": "markdown", "source": ["# importing model with pickle"], "metadata": {"id": "Cdh_XlhlWEZc"}}, {"cell_type": "code", "source": ["import pickle\n", "\n", "# Assuming 'model' is your trained model object\n", "filename = 'trained_knn_model.sav'\n", "pickle.dump(kn, open(filename, 'wb'))"], "metadata": {"id": "5H4INqozdxbD"}, "execution_count": 68, "outputs": []}, {"cell_type": "code", "source": ["filename = 'trained_knn_model.sav'\n", "loaded_model = pickle.load(open(filename, 'rb'))"], "metadata": {"id": "vE--Ju3OK2G7"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["data=xtest.iloc[15]\n", "data=data.to_list()\n", "data"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "w5QhunAILFnC", "outputId": "6d234084-2a60-40d7-bbc8-a6c649928d6b"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 9, 2, 2, 2, 2, 2, 2]"]}, "metadata": {}, "execution_count": 64}]}, {"cell_type": "code", "source": ["print(\"pickle model res=\",loaded_model.predict([data]))\n", "print(\"kn model res=\",kn.predict([data]))"], "metadata": {"id": "-dVHtF94dxXD", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "a9e8c19a-a446-4b38-948a-58d09f4afcf5", "collapsed": true}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["pickle model res= ['Customer Service Executive']\n", "kn model res= ['Customer Service Executive']\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/sklearn/utils/validation.py:2739: UserWarning: X does not have valid feature names, but KNeighborsClassifier was fitted with feature names\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/sklearn/utils/validation.py:2739: UserWarning: X does not have valid feature names, but KNeighborsClassifier was fitted with feature names\n", "  warnings.warn(\n"]}]}, {"cell_type": "markdown", "source": ["# Random forest\n"], "metadata": {"id": "1mPn_bxk___0"}}, {"cell_type": "code", "source": ["import pandas as pd\n", "\n", "# Assuming your CSV file is named 'your_file.csv'\n", "\n", "df = pd.read_csv('dataset9000.data')\n", "df.columns=[\"Database Fundamentals\",\"Computer Architecture\",\"Distributed Computing Systems\",\n", "\"Cyber-Security\",\"Networking\",\"Development\",\"Programming Skills\",\"Project Management\",\n", "\"Computer Forensics Fundamentals\",\"Technical Communication\",\"AI ML\",\"Software Engineering\",\"Business Analysis\",\n", "\"Communication skills\",\"Data Science\",\"Troubleshooting-skills\",\"Graphics Designing\",\"Roles\"]\n", "# df=df.replace({5:4 , 6:5, 7:6 , 9:7})\n", "df.head()\n"], "metadata": {"id": "6E6btJ4imVGT", "colab": {"base_uri": "https://localhost:8080/", "height": 348}, "collapsed": true, "outputId": "8d4a0aa1-f217-4c30-e6b0-aa816272d55b"}, "execution_count": 69, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   Database Fundamentals  Computer Architecture  \\\n", "0                      9                      2   \n", "1                      9                      3   \n", "2                      9                      5   \n", "3                      9                      6   \n", "4                      9                      7   \n", "\n", "   Distributed Computing Systems  Cyber-Security  Networking  Development  \\\n", "0                              1               1           1            1   \n", "1                              1               1           1            1   \n", "2                              1               1           1            1   \n", "3                              1               1           1            1   \n", "4                              1               1           1            1   \n", "\n", "   Programming Skills  Project Management  Computer Forensics Fundamentals  \\\n", "0                   1                   1                                1   \n", "1                   1                   1                                1   \n", "2                   1                   1                                1   \n", "3                   1                   1                                1   \n", "4                   1                   1                                1   \n", "\n", "   Technical Communication  AI ML  Software Engineering  Business Analysis  \\\n", "0                        1      1                     1                  1   \n", "1                        1      1                     1                  1   \n", "2                        1      1                     1                  1   \n", "3                        1      1                     1                  1   \n", "4                        1      1                     1                  1   \n", "\n", "   Communication skills  Data Science  Troubleshooting-skills  \\\n", "0                     1             1                       1   \n", "1                     1             1                       1   \n", "2                     1             1                       1   \n", "3                     1             1                       1   \n", "4                     1             1                       1   \n", "\n", "   Graphics Designing                   Roles  \n", "0                   1  Database Administrator  \n", "1                   1  Database Administrator  \n", "2                   1  Database Administrator  \n", "3                   1  Database Administrator  \n", "4                   1  Database Administrator  "], "text/html": ["\n", "  <div id=\"df-e055e49e-7b44-4ff4-8aa6-71b3b8a1390b\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Database Fundamentals</th>\n", "      <th>Computer Architecture</th>\n", "      <th>Distributed Computing Systems</th>\n", "      <th>Cyber-Security</th>\n", "      <th>Networking</th>\n", "      <th>Development</th>\n", "      <th>Programming Skills</th>\n", "      <th>Project Management</th>\n", "      <th>Computer Forensics Fundamentals</th>\n", "      <th>Technical Communication</th>\n", "      <th>AI ML</th>\n", "      <th>Software Engineering</th>\n", "      <th>Business Analysis</th>\n", "      <th>Communication skills</th>\n", "      <th>Data Science</th>\n", "      <th>Troubleshooting-skills</th>\n", "      <th>Graphics Designing</th>\n", "      <th>Roles</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>9</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>9</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>9</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>9</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Database Administrator</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-e055e49e-7b44-4ff4-8aa6-71b3b8a1390b')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-e055e49e-7b44-4ff4-8aa6-71b3b8a1390b button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-e055e49e-7b44-4ff4-8aa6-71b3b8a1390b');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-907bfad6-c125-48e2-b5cd-ddaca647708e\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-907bfad6-c125-48e2-b5cd-ddaca647708e')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-907bfad6-c125-48e2-b5cd-ddaca647708e button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 9179,\n  \"fields\": [\n    {\n      \"column\": \"Database Fundamentals\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          9,\n          1,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Computer Architecture\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          2,\n          3,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Distributed Computing Systems\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Cyber-Security\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Networking\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Development\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Programming Skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Project Management\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Computer Forensics Fundamentals\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Technical Communication\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"AI ML\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Software Engineering\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Business Analysis\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Communication skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Data Science\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Troubleshooting-skills\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Graphics Designing\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2,\n        \"min\": 1,\n        \"max\": 9,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          1,\n          2,\n          7\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Roles\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 17,\n        \"samples\": [\n          \"Database Administrator\",\n          \"Hardware Engineer\",\n          \"Software Developer\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 69}]}, {"cell_type": "code", "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import accuracy_score, confusion_matrix, classification_report\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n"], "metadata": {"id": "Zu6jEegK2n2N"}, "execution_count": 70, "outputs": []}, {"cell_type": "code", "source": ["rf= RandomForestClassifier(n_estimators=50 , max_depth=10)"], "metadata": {"id": "VNY1frWR2nxH"}, "execution_count": 72, "outputs": []}, {"cell_type": "code", "source": ["x=df.iloc[:,:17]\n", "y=df.iloc[:,17]\n", "xtrain,xtest,ytrain,ytest = train_test_split(x,y,test_size=0.2,random_state=10)"], "metadata": {"id": "Ww2ZojxOdxUS"}, "execution_count": 71, "outputs": []}, {"cell_type": "code", "source": ["rf.fit(X=xtrain,y=ytrain)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 80}, "id": "QeHgP9-lm5rf", "outputId": "efabf7a9-8278-4293-c677-73259073fa5a"}, "execution_count": 73, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["RandomForestClassifier(max_depth=10, n_estimators=50)"], "text/html": ["<style>#sk-container-id-2 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-2 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-2 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-2 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-2 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-2 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-2 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-2 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-2 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-2 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-2 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-2 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-2 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-2 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-2 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-2 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-2 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomForestClassifier(max_depth=10, n_estimators=50)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>RandomForestClassifier</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.6/modules/generated/sklearn.ensemble.RandomForestClassifier.html\">?<span>Documentation for RandomForestClassifier</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestClassifier(max_depth=10, n_estimators=50)</pre></div> </div></div></div></div>"]}, "metadata": {}, "execution_count": 73}]}, {"cell_type": "code", "source": ["res=rf.predict(xtest)\n", "print(\"accuracy score:\",accuracy_score(ytest,res))\n", "print(\"accuracy of confusion matrix:\", confusion_matrix(ytest,res))\n", "# print(\"R2 score:\", r2_score(ytest,res))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MLhCvyN1m5oI", "outputId": "f5918d22-2fb9-456f-e5a5-0542ccd4c53a"}, "execution_count": 74, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["accuracy score: 1.0\n", "accuracy of confusion matrix: [[102   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0 119   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0 104   0   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0  99   0   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0 101   0   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0 113   0   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0 108   0   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0 121   0   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0 104   0   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0 109   0   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0 110   0   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0 107   0   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0 100   0   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0 111   0   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0 109   0   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0 111   0]\n", " [  0   0   0   0   0   0   0   0   0   0   0   0   0   0   0   0 108]]\n"]}]}, {"cell_type": "markdown", "source": ["Accuracy of Random forest with 50,estimators and 10,max_depth is 100%\n"], "metadata": {"id": "sTHhDdqQt5OK"}}, {"cell_type": "code", "source": ["# now for optimum hyperparameter tuning\n", "mul_li=[]\n", "for est in range(5,20):\n", "  score_li=[]\n", "  for depth in range(3,8):\n", "    rff=RandomForestClassifier(n_estimators=est ,max_depth=depth)\n", "    rff.fit(X=xtrain,y=ytrain)\n", "    prediction= rff.predict(xtest)\n", "    score_li.append(accuracy_score(ytest,prediction))\n", "  mul_li.append(score_li)"], "metadata": {"id": "3HEeRHAAm5i5"}, "execution_count": 109, "outputs": []}, {"cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "# plt.plot(range(1,91), score_li)\n", "\n", "plt.plot(range(3,8),mul_li[0], label='n=5')\n", "plt.plot(range(3,8), mul_li[1], label='n=6')\n", "plt.plot(range(3,8), mul_li[2], label='n=7')\n", "plt.plot(range(3,8),mul_li[3], label='n=8')\n", "plt.plot(range(3,8), mul_li[4], label='n=9')\n", "plt.plot(range(3,8), mul_li[5], label='n=10')\n", "plt.xlabel(\"Depths\")\n", "plt.ylabel(\"Accuracy\")\n", "plt.title(\"RF Accuracy vs. Number of estimator and depth\")\n", "plt.legend()\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 472}, "id": "LNHvj0RDarwM", "outputId": "fee9559d-e3f7-4e26-fd08-0764060b52c8"}, "execution_count": 111, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}]}