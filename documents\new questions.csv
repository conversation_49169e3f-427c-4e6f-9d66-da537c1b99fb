"Q No.","Skill","Question","Option A","Weight A","Option B","Weight B","Option C","Weight C","Option D","Weight D"
"1","Database Fundamentals","Which SQL statement is used to extract data from a database?","SELECT","3","EXTRACT","1","GET","2","REMOVE","0"
"2","Database Fundamentals","What is a primary key?","A unique identifier for a record","3","A type of encryption","0","A backup method","1","A sorting algorithm","2"
"3","Database Fundamentals","Which of the following best describes normalization?","Organizing data to reduce redundancy","3","Making data uppercase","0","Adding more tables","2","Encrypting data","1"
"4","Computer Architecture","What does the CPU stand for?","Central Processing Unit","3","Computer Power Unit","1","Central Programming Unit","2","Computer Processing Utility","0"
"5","Computer Architecture","Which component stores the BIOS?","ROM","3","RAM","2","Hard Disk","1","CPU","0"
"6","Computer Architecture","What is the function of the ALU?","Performs arithmetic and logic operations","3","Manages memory","1","Controls input/output","2","Displays graphics","0"
"7","Distributed Computing Systems","What is the main benefit of distributed systems?","Improved scalability and fault tolerance","3","Easier to program","1","Uses less electricity","2","Cheaper hardware","0"
"8","Distributed Computing Systems","Which protocol is commonly used for distributed file systems?","NFS","3","HTTP","2","FTP","1","SMTP","0"
"9","Distributed Computing Systems","What is a challenge in distributed computing?","Network latency","3","Faster CPUs","1","Local storage","2","More RAM","0"
"10","Cyber Security","Which of these is the best example of two-factor authentication?","Password only","0","Password + SMS code","3","Security question","1","Email address only","2"
"11","Cyber Security","You receive an email from your bank asking for your password. What should you do?","Reply with your password","0","Report it as phishing","3","Ignore the email","2","Forward it to your friend","1"
"12","Cyber Security","Which tool is commonly used to scan a system for vulnerabilities?","MS Paint","0","Nmap","3","Notepad","1","Calculator","2"
"13","Networking","What does the acronym LAN stand for?","Local Area Network","3","Large Area Network","2","Long Access Node","1","Local Access Network","0"
"14","Networking","Which device forwards data packets between computer networks?","Router","3","Switch","2","Hub","1","Monitor","0"
"15","Networking","What is the purpose of the OSI model?","To standardize network communication","3","To increase bandwidth","1","To encrypt data","2","To speed up computers","0"
"16","Software Development","What is version control used for?","Tracking changes in code","3","Speeding up code","1","Debugging code","2","Encrypting code","0"
"17","Software Development","Which methodology uses sprints and daily stand-ups?","Agile","3","Waterfall","1","Spiral","2","V-Model","0"
"18","Software Development","What is the main purpose of unit testing?","Test individual components","3","Test user interfaces","2","Test network speed","1","Test hardware","0"
"19","Programming Skills","What does the following Python code output?print([i**2 for i in range(3)])","[0,","[1,","[1,","Syntax Error","0","","",""
"20","Programming Skills","You need to reverse a string in Python. Which method is most efficient?","Use slicing:s[::-1]","3","Use a for loop to build a new string","2","Usereversed(s)and join","1","Sort the string","0"
"21","Programming Skills","What is the key distinction between a list and a tuple in Python?","Lists are mutable, tuples are immutable","3","Tuples are always longer than lists","2","Lists are faster than tuples","1","Tuples can only contain numbers","0"
"22","AI ML","What is supervised learning?","Learning from labeled data","3","Learning from unlabeled data","2","Learning by observation","1","Learning by memorization","0"
"23","AI ML","Which library is commonly used for machine learning in Python?","scikit-learn","3","matplotlib","2","pandas","1","flask","0"
"24","AI ML","What is overfitting?","Model fits training data too well, poor on new data","3","Model fits all data perfectly","1","Model is too simple","2","Model is always accurate","0"
"25","Software Engineering","What is the main goal of software engineering?","Develop reliable and efficient software","3","Write code quickly","1","Use latest technology","2","Reduce hardware costs","0"
"26","Software Engineering","What is refactoring?","Improving code without changing its behavior","3","Adding new features","2","Fixing bugs","1","Deleting code","0"
"27","Software Engineering","What is the purpose of a software requirements specification (SRS)?","Document functional/non-functional requirements","3","List programming languages","1","Describe hardware","2","Provide user manuals","0"
"28","Business Analysis","What is a use case diagram used for?","Visualizing system interactions","3","Designing databases","2","Coding algorithms","1","Testing software","0"
"29","Business Analysis","What is the first step in business analysis?","Gather requirements","3","Design database","2","Write code","1","Deploy software","0"
"30","Business Analysis","What does SWOT stand for?","Strengths, Weaknesses, Opportunities, Threats","3","Software, Web, Organization, Testing","2","Systems, Workflow, Operations, Technology","1","Security, Work, Output, Testing","0"
"31","Data Science","What is data cleaning?","Removing or correcting inaccurate data","3","Encrypting data","1","Visualizing data","2","Storing data","0"
"32","Data Science","Which language is most commonly used for data science?","Python","3","Java","2","HTML","1","PHP","0"
"33","Data Science","What is the purpose of data visualization?","To communicate insights from data","3","To store data","1","To encrypt data","2","To delete data","0"